'use client';

import React from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';
import ReadStatus from './ReadStatus';

// Test component to verify click-outside functionality
const TestContainer = styled.div`
  padding: ${appTheme.spacing.xl};
  background: ${appTheme.colors.background.lighter};
  min-height: 100vh;
`;

const TestTitle = styled.h2`
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.lg};
  font-size: 24px;
  font-weight: 600;
`;

const TestSection = styled.div`
  margin-bottom: ${appTheme.spacing.xl};
  padding: ${appTheme.spacing.lg};
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  border: 1px solid ${appTheme.colors.border};
`;

const TestSectionTitle = styled.h3`
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.md};
  font-size: 18px;
  font-weight: 500;
`;

const InstructionBox = styled.div`
  margin-bottom: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.md};
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: ${appTheme.borderRadius.md};
  font-size: 14px;
  color: #0369a1;
  line-height: 1.5;
`;

const MockMessageGroup = styled.div<{ $isOwn: boolean }>`
  display: flex;
  align-items: flex-start;
  gap: ${appTheme.spacing.md};
  flex-direction: ${props => (props.$isOwn ? 'row-reverse' : 'row')};
  margin-bottom: ${appTheme.spacing.xl};
  padding: 0 ${appTheme.spacing.xs};
`;

const MockAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
`;

const MockMessageContent = styled.div<{ $isOwn: boolean }>`
  max-width: 65%;
  display: flex;
  flex-direction: column;
  gap: 0;
  min-width: 0;
  position: relative;
`;

const MockMessageBubble = styled.div<{ $isOwn: boolean }>`
  background: ${props => 
    props.$isOwn 
      ? `linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%)`
      : appTheme.colors.background.main
  };
  color: ${props => (props.$isOwn ? 'white' : appTheme.colors.text.primary)};
  padding: ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.lg};
  border: ${props => (props.$isOwn ? 'none' : `1px solid ${appTheme.colors.border}`)};
  box-shadow: ${appTheme.shadows.sm};
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
`;

const MockMessageText = styled.p`
  margin: 0;
  line-height: 1.5;
  font-size: 14px;
`;

const MockMessageFooter = styled.div<{ $isOwn: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  margin-top: ${appTheme.spacing.sm};
  justify-content: ${props => (props.$isOwn ? 'flex-end' : 'flex-start')};
  opacity: 0.8;
  transition: opacity 0.2s ease;
  padding: 0 ${appTheme.spacing.xs};

  &:hover {
    opacity: 1;
  }
`;

const MockMessageTime = styled.span<{ $isOwn: boolean }>`
  font-size: 11px;
  font-weight: 500;
  color: ${props => (props.$isOwn ? 'rgba(255, 255, 255, 0.9)' : appTheme.colors.text.secondary)};
  text-shadow: ${props => (props.$isOwn ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none')};
  letter-spacing: 0.02em;
  user-select: none;
  white-space: nowrap;
  transition: color 0.2s ease;

  &:hover {
    color: ${props => (props.$isOwn ? 'rgba(255, 255, 255, 1)' : appTheme.colors.text.primary)};
  }
`;

const MockMessageStatus = styled.span<{ $isOwn: boolean }>`
  color: ${props => (props.$isOwn ? appTheme.colors.text.light : 'transparent')};
  font-size: 12px;
  display: flex;
  align-items: center;
`;

const ClickableArea = styled.div`
  padding: ${appTheme.spacing.lg};
  margin: ${appTheme.spacing.md} 0;
  background: ${appTheme.colors.background.lighter};
  border: 2px dashed ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  text-align: center;
  color: ${appTheme.colors.text.secondary};
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.light};
    border-color: ${appTheme.colors.primary};
    color: ${appTheme.colors.text.primary};
  }
`;

// Mock read status data
const mockReadStatusGroup = {
  readByUsers: [
    {
      user: {
        id: 2,
        firstName: 'Jane',
        lastName: 'Smith',
        imageUrl: undefined,
      },
      readAt: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
    },
    {
      user: {
        id: 3,
        firstName: 'Bob',
        lastName: 'Johnson',
        imageUrl: undefined,
      },
      readAt: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
    },
  ],
  unreadUsers: [
    {
      id: 4,
      firstName: 'Alice',
      lastName: 'Brown',
      imageUrl: undefined,
    },
  ],
  totalParticipants: 3,
  readCount: 2,
  unreadCount: 1,
};

const ReadStatusClickTest: React.FC = () => {
  return (
    <TestContainer>
      <TestTitle>ReadStatus Click-Outside Test</TestTitle>
      
      <TestSection>
        <TestSectionTitle>🖱️ Click-Outside Functionality Test</TestSectionTitle>
        
        <InstructionBox>
          <strong>How to test:</strong>
          <ol style={{ margin: '8px 0 0 20px', paddingLeft: 0 }}>
            <li>Click the read status indicator (✓✓ 2/3) below to open the tooltip</li>
            <li>Try clicking anywhere outside the tooltip to close it</li>
            <li>Try pressing the <kbd style={{ background: '#e5e7eb', padding: '2px 6px', borderRadius: '4px' }}>Escape</kbd> key to close it</li>
            <li>Notice that clicking inside the tooltip content doesn't close it</li>
            <li>Test on both desktop and mobile devices</li>
          </ol>
        </InstructionBox>

        <MockMessageGroup $isOwn={true}>
          <MockAvatar>JD</MockAvatar>
          <MockMessageContent $isOwn={true}>
            <MockMessageBubble $isOwn={true}>
              <MockMessageText>
                Click the read status below to test the click-outside functionality!
              </MockMessageText>
            </MockMessageBubble>
            <MockMessageFooter $isOwn={true}>
              <MockMessageTime $isOwn={true}>2:25 PM</MockMessageTime>
              <MockMessageStatus $isOwn={true}>✓✓</MockMessageStatus>
              <ReadStatus
                readStatus={mockReadStatusGroup}
                isOwn={true}
                isGroupChat={true}
                messageId="test-click-outside"
              />
            </MockMessageFooter>
          </MockMessageContent>
        </MockMessageGroup>

        <ClickableArea>
          👆 Click here (outside the tooltip) to test the click-outside functionality
        </ClickableArea>

        <ClickableArea>
          🎯 This is another clickable area to test closing the tooltip
        </ClickableArea>
      </TestSection>

      <TestSection>
        <TestSectionTitle>⌨️ Keyboard Accessibility</TestSectionTitle>
        
        <InstructionBox>
          <strong>Keyboard shortcuts:</strong>
          <ul style={{ margin: '8px 0 0 20px', paddingLeft: 0 }}>
            <li><kbd style={{ background: '#e5e7eb', padding: '2px 6px', borderRadius: '4px' }}>Escape</kbd> - Close the tooltip</li>
            <li><kbd style={{ background: '#e5e7eb', padding: '2px 6px', borderRadius: '4px' }}>Click</kbd> - Toggle tooltip open/closed</li>
          </ul>
        </InstructionBox>

        <MockMessageGroup $isOwn={true}>
          <MockAvatar>KB</MockAvatar>
          <MockMessageContent $isOwn={true}>
            <MockMessageBubble $isOwn={true}>
              <MockMessageText>
                Test keyboard accessibility with this message's read status.
              </MockMessageText>
            </MockMessageBubble>
            <MockMessageFooter $isOwn={true}>
              <MockMessageTime $isOwn={true}>2:30 PM</MockMessageTime>
              <MockMessageStatus $isOwn={true}>✓✓</MockMessageStatus>
              <ReadStatus
                readStatus={mockReadStatusGroup}
                isOwn={true}
                isGroupChat={true}
                messageId="test-keyboard"
              />
            </MockMessageFooter>
          </MockMessageContent>
        </MockMessageGroup>
      </TestSection>

      <TestSection>
        <TestSectionTitle>📱 Mobile vs Desktop Behavior</TestSectionTitle>
        
        <InstructionBox>
          <strong>Expected behavior:</strong>
          <ul style={{ margin: '8px 0 0 20px', paddingLeft: 0 }}>
            <li><strong>Desktop:</strong> Transparent overlay for click detection, tooltip positioned above footer</li>
            <li><strong>Mobile:</strong> Semi-transparent backdrop overlay, full-screen centered tooltip</li>
            <li><strong>Both:</strong> Click outside or press Escape to close</li>
          </ul>
        </InstructionBox>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: appTheme.spacing.md }}>
          <MockMessageGroup $isOwn={true}>
            <MockAvatar>DT</MockAvatar>
            <MockMessageContent $isOwn={true}>
              <MockMessageBubble $isOwn={true}>
                <MockMessageText>Desktop test message</MockMessageText>
              </MockMessageBubble>
              <MockMessageFooter $isOwn={true}>
                <MockMessageTime $isOwn={true}>2:35 PM</MockMessageTime>
                <MockMessageStatus $isOwn={true}>✓✓</MockMessageStatus>
                <ReadStatus
                  readStatus={mockReadStatusGroup}
                  isOwn={true}
                  isGroupChat={true}
                  messageId="test-desktop"
                />
              </MockMessageFooter>
            </MockMessageContent>
          </MockMessageGroup>

          <MockMessageGroup $isOwn={true}>
            <MockAvatar>MB</MockAvatar>
            <MockMessageContent $isOwn={true}>
              <MockMessageBubble $isOwn={true}>
                <MockMessageText>Mobile test message</MockMessageText>
              </MockMessageBubble>
              <MockMessageFooter $isOwn={true}>
                <MockMessageTime $isOwn={true}>2:37 PM</MockMessageTime>
                <MockMessageStatus $isOwn={true}>✓✓</MockMessageStatus>
                <ReadStatus
                  readStatus={mockReadStatusGroup}
                  isOwn={true}
                  isGroupChat={true}
                  messageId="test-mobile"
                />
              </MockMessageFooter>
            </MockMessageContent>
          </MockMessageGroup>
        </div>
      </TestSection>
    </TestContainer>
  );
};

export default ReadStatusClickTest;
