'use client';

import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Bold, List, Type } from 'lucide-react';
import { appTheme } from '@/app/theme';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
}

interface FormatButton {
  type: 'bold' | 'list' | 'normal';
  icon: React.ReactNode;
  label: string;
}

const EditorContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  border: none;
  outline: none;
  background: transparent;
`;

const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} 0;
  border-bottom: 1px solid ${appTheme.colors.border};
  margin-bottom: ${appTheme.spacing.xs};
`;

const FormatButtonStyled = styled.button<{ $active?: boolean }>`
  padding: ${appTheme.spacing.xs};
  border: none;
  background: ${props => props.$active ? appTheme.colors.primary : 'transparent'};
  color: ${props => props.$active ? 'white' : appTheme.colors.text.secondary};
  border-radius: ${appTheme.borderRadius.sm};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  min-height: 28px;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.primaryHover : appTheme.colors.background.lighter};
    color: ${props => props.$active ? 'white' : appTheme.colors.text.primary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const EditorTextArea = styled.div`
  flex: 1;
  min-height: 20px;
  max-height: 120px;
  overflow-y: auto;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.4;
  padding: ${appTheme.spacing.xs} 0;
  background: transparent;
  border: none;
  outline: none;
  white-space: pre-wrap;
  word-wrap: break-word;

  &:empty::before {
    content: attr(data-placeholder);
    color: ${appTheme.colors.text.light};
    pointer-events: none;
  }

  /* Mobile - larger font and better touch experience */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
    min-height: 24px;
    max-height: 100px;
    padding: ${appTheme.spacing.sm} 0;
  }

  /* Small mobile - optimize for smaller screens */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    max-height: 80px;
    padding: ${appTheme.spacing.xs} 0;
  }

  /* Rich text formatting styles */
  .bold {
    font-weight: bold;
  }

  .normal {
    font-weight: normal;
  }

  ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
  }

  li {
    margin: 2px 0;
  }
`;

const formatButtons: FormatButton[] = [
  { type: 'bold', icon: <Bold size={14} />, label: 'Bold' },
  { type: 'normal', icon: <Type size={14} />, label: 'Normal' },
  { type: 'list', icon: <List size={14} />, label: 'Bullet List' },
];

export default function RichTextEditor({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Type a message...',
  disabled = false,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set());

  // Convert plain text to HTML for display
  const convertTextToHtml = (text: string): string => {
    if (!text) return '';
    
    // Handle bullet points (lines starting with • or -)
    const lines = text.split('\n');
    let html = '';
    let inList = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const isBulletPoint = line.trim().startsWith('•') || line.trim().startsWith('-');
      
      if (isBulletPoint) {
        if (!inList) {
          html += '<ul>';
          inList = true;
        }
        const content = line.replace(/^[\s]*[•-][\s]*/, '');
        html += `<li>${content}</li>`;
      } else {
        if (inList) {
          html += '</ul>';
          inList = false;
        }
        if (line.trim()) {
          html += `<div>${line}</div>`;
        } else {
          html += '<div><br></div>';
        }
      }
    }
    
    if (inList) {
      html += '</ul>';
    }
    
    return html;
  };

  // Convert HTML back to plain text for storage
  const convertHtmlToText = (html: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    // Handle lists
    const lists = tempDiv.querySelectorAll('ul');
    lists.forEach(list => {
      const items = list.querySelectorAll('li');
      items.forEach(item => {
        item.textContent = '• ' + (item.textContent || '');
      });
    });
    
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Update editor content when value changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== convertTextToHtml(value)) {
      editorRef.current.innerHTML = convertTextToHtml(value);
    }
  }, [value]);

  // Handle input changes
  const handleInput = () => {
    if (editorRef.current) {
      const htmlContent = editorRef.current.innerHTML;
      const textContent = convertHtmlToText(htmlContent);
      onChange(textContent);
    }
  };

  // Handle format button clicks
  const handleFormat = (formatType: string) => {
    if (disabled) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    switch (formatType) {
      case 'bold':
        document.execCommand('bold', false);
        break;
      case 'normal':
        // Remove bold formatting
        if (document.queryCommandState('bold')) {
          document.execCommand('bold', false);
        }
        break;
      case 'list':
        document.execCommand('insertUnorderedList', false);
        break;
    }

    // Update active formats
    updateActiveFormats();
    handleInput();
  };

  // Update active format states
  const updateActiveFormats = () => {
    const formats = new Set<string>();
    
    if (document.queryCommandState('bold')) {
      formats.add('bold');
    }
    if (document.queryCommandState('insertUnorderedList')) {
      formats.add('list');
    }
    
    setActiveFormats(formats);
  };

  // Handle selection change to update active formats
  useEffect(() => {
    const handleSelectionChange = () => {
      updateActiveFormats();
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    return () => document.removeEventListener('selectionchange', handleSelectionChange);
  }, []);

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    // Ctrl/Cmd + B for bold
    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
      e.preventDefault();
      handleFormat('bold');
      return;
    }

    // Call parent onKeyDown handler
    if (onKeyDown) {
      onKeyDown(e);
    }
  };

  return (
    <EditorContainer>
      <ToolbarContainer>
        {formatButtons.map((button) => (
          <FormatButtonStyled
            key={button.type}
            $active={activeFormats.has(button.type)}
            onClick={() => handleFormat(button.type)}
            disabled={disabled}
            title={button.label}
            type="button"
          >
            {button.icon}
          </FormatButtonStyled>
        ))}
      </ToolbarContainer>
      
      <EditorTextArea
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />
    </EditorContainer>
  );
}
